import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Bot,
  Send,
  User,
  Sparkles,
  Loader2,
  Copy,
  Check,
  RefreshCw,
  FileText,
  Lightbulb,
  AlertCircle,
  Wand2
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { DocumentEngine } from "@/engines/document-engine";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  contractContent?: string;
}

interface AIContractGeneratorProps {
  onContractGenerated?: (content: string) => void;
  onNavigateToEditor?: (content: string) => void;
  className?: string;
}

const AIContractGenerator: React.FC<AIContractGeneratorProps> = ({
  onContractGenerated,
  onNavigateToEditor,
  className = ""
}) => {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: "Hello! I'm your AI contract assistant. Describe the contract you need, and I'll help you draft it. Be as specific as possible about the terms, parties, and requirements.",
      timestamp: new Date().toISOString()
    }
  ]);
  const [generatedContract, setGeneratedContract] = useState<string>("");
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const examplePrompts = [
    "Create a freelance web development contract for a 6-month project with milestone payments",
    "Draft an NDA for sharing confidential business information with potential investors",
    "Generate a service agreement for monthly marketing consulting with performance metrics",
    "Create an employment contract for a remote software developer position",
    "Draft a partnership agreement for a joint venture in e-commerce",
    "Generate a rental agreement for commercial office space"
  ];

  const handleSendPrompt = async () => {
    if (!prompt.trim() || isGenerating) return;

    const userMessage: Message = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      role: "user",
      content: prompt.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsGenerating(true);
    setPrompt("");

    try {
      // Simulate AI processing with more realistic delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      const response = await generateAIResponse(userMessage.content);
      
      const aiMessage: Message = {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        role: "assistant",
        content: response.message,
        timestamp: new Date().toISOString(),
        contractContent: response.contract
      };

      setMessages(prev => [...prev, aiMessage]);

      if (response.contract) {
        setGeneratedContract(response.contract);
        if (onContractGenerated) {
          onContractGenerated(response.contract);
        }
      }

      toast({
        title: "Contract Generated",
        description: "Your contract has been generated successfully. Review it below.",
      });

    } catch (error) {
      const errorMessage: Message = {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        role: "assistant",
        content: "I apologize, but I encountered an error while generating your contract. Please try again with a different prompt or contact support if the issue persists.",
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Generation Failed",
        description: "Failed to generate contract. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendPrompt();
    }
  };

  const handleCopyContract = async () => {
    if (!generatedContract) return;

    try {
      await navigator.clipboard.writeText(generatedContract);
      setCopiedToClipboard(true);
      setTimeout(() => setCopiedToClipboard(false), 2000);
      
      toast({
        title: "Copied to Clipboard",
        description: "Contract content has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy contract to clipboard.",
        variant: "destructive"
      });
    }
  };

  const handleUsePrompt = (examplePrompt: string) => {
    setPrompt(examplePrompt);
  };

  const handleRegenerateContract = async () => {
    if (!messages.length || isGenerating) return;

    const lastUserMessage = messages.filter(m => m.role === 'user').pop();
    if (!lastUserMessage) return;

    setIsGenerating(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 2500));

      const response = await generateAIResponse(lastUserMessage.content, true);
      
      const aiMessage: Message = {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        role: "assistant",
        content: "I've regenerated the contract with some variations. " + response.message,
        timestamp: new Date().toISOString(),
        contractContent: response.contract
      };

      setMessages(prev => [...prev, aiMessage]);

      if (response.contract) {
        setGeneratedContract(response.contract);
        if (onContractGenerated) {
          onContractGenerated(response.contract);
        }
      }

      toast({
        title: "Contract Regenerated",
        description: "A new version of your contract has been generated.",
      });

    } catch (error) {
      toast({
        title: "Regeneration Failed",
        description: "Failed to regenerate contract. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const generateAIResponse = async (userPrompt: string, isRegeneration = false): Promise<{ message: string, contract?: string }> => {
    // Enhanced mock AI response generation
    const isContractRequest =
      userPrompt.toLowerCase().includes("contract") ||
      userPrompt.toLowerCase().includes("agreement") ||
      userPrompt.toLowerCase().includes("draft") ||
      userPrompt.toLowerCase().includes("create") ||
      userPrompt.toLowerCase().includes("generate");

    if (isContractRequest) {
      const contractType = detectContractType(userPrompt);
      const contract = generateContractTemplate(contractType, userPrompt);

      const responseMessage = isRegeneration 
        ? "Here's an updated version of your contract with some modifications."
        : `I've drafted a ${contractType} based on your requirements. The contract includes standard clauses and terms that you can customize further.`;

      return {
        message: responseMessage,
        contract: contract
      };
    }

    return {
      message: "I can help you draft contracts and agreements. Could you provide more details about the type of contract you need? For example, specify the parties involved, duration, payment terms, and key obligations."
    };
  };

  const detectContractType = (prompt: string): string => {
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes("freelance") || lowerPrompt.includes("independent contractor")) return "Freelance Agreement";
    if (lowerPrompt.includes("nda") || lowerPrompt.includes("non-disclosure")) return "Non-Disclosure Agreement";
    if (lowerPrompt.includes("employment") || lowerPrompt.includes("job")) return "Employment Contract";
    if (lowerPrompt.includes("service") || lowerPrompt.includes("consulting")) return "Service Agreement";
    if (lowerPrompt.includes("partnership") || lowerPrompt.includes("joint venture")) return "Partnership Agreement";
    if (lowerPrompt.includes("rental") || lowerPrompt.includes("lease")) return "Rental Agreement";
    if (lowerPrompt.includes("sales") || lowerPrompt.includes("purchase")) return "Sales Agreement";
    
    return "Service Agreement";
  };

  const generateContractTemplate = (contractType: string, prompt: string): string => {
    const currentDate = new Date().toLocaleDateString();
    
    return `# ${contractType.toUpperCase()}

**Date:** ${currentDate}

## PARTIES

**Party A (Client):** [CLIENT NAME]
Address: [CLIENT ADDRESS]
Email: [CLIENT EMAIL]

**Party B (Service Provider):** [PROVIDER NAME]  
Address: [PROVIDER ADDRESS]
Email: [PROVIDER EMAIL]

## 1. SCOPE OF WORK

Based on your requirements: "${prompt.substring(0, 100)}..."

The Service Provider agrees to provide the following services:
- [Detailed description of services]
- [Specific deliverables]
- [Timeline and milestones]

## 2. COMPENSATION

**Total Contract Value:** $[AMOUNT]
**Payment Schedule:** [PAYMENT TERMS]
- [Payment milestone 1]
- [Payment milestone 2]
- [Final payment upon completion]

## 3. TIMELINE

**Project Start Date:** [START DATE]
**Project End Date:** [END DATE]
**Key Milestones:**
- [Milestone 1]: [Date]
- [Milestone 2]: [Date]
- [Final Delivery]: [Date]

## 4. RESPONSIBILITIES

### Client Responsibilities:
- Provide necessary information and materials
- Review and approve deliverables in a timely manner
- Make payments according to the agreed schedule

### Service Provider Responsibilities:
- Deliver services according to specifications
- Meet agreed-upon deadlines
- Maintain professional standards

## 5. INTELLECTUAL PROPERTY

[IP ownership and usage rights]

## 6. CONFIDENTIALITY

Both parties agree to maintain confidentiality of proprietary information.

## 7. TERMINATION

This agreement may be terminated by either party with [NOTICE PERIOD] written notice.

## 8. GOVERNING LAW

This agreement shall be governed by the laws of [JURISDICTION].

## SIGNATURES

**Client Signature:** ___________________________ **Date:** ___________

**Service Provider Signature:** ___________________________ **Date:** ___________

---
*This contract was generated by AI and should be reviewed by legal counsel before execution.*`;
  };

  return (
    <div className={`ai-contract-generator ${className}`}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[600px]">
        {/* Chat Interface */}
        <Card className="flex flex-col">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4 text-purple-600" />
              </div>
              <CardTitle className="text-lg">AI Assistant</CardTitle>
              <Badge variant="secondary" className="ml-auto">
                {isGenerating ? "Generating..." : "Ready"}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="flex-1 flex flex-col p-0">
            {/* Messages */}
            <ScrollArea className="flex-1 px-4">
              <div className="space-y-4 pb-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    {message.role === 'assistant' && (
                      <Avatar className="w-8 h-8 mt-1">
                        <AvatarFallback className="bg-purple-100 text-purple-600">
                          <Bot className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                    
                    <div className={`max-w-[80%] ${message.role === 'user' ? 'order-first' : ''}`}>
                      <div
                        className={`rounded-lg p-3 text-sm ${
                          message.role === 'user'
                            ? 'bg-primary text-primary-foreground ml-auto'
                            : 'bg-muted'
                        }`}
                      >
                        {message.content}
                      </div>
                      
                      {message.contractContent && (
                        <div className="mt-2 p-2 bg-green-50 dark:bg-green-950/30 rounded border border-green-200 dark:border-green-800">
                          <div className="flex items-center gap-2 text-xs text-green-700 dark:text-green-300 mb-1">
                            <FileText className="h-3 w-3" />
                            Contract Generated
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-7 text-xs"
                              onClick={handleCopyContract}
                            >
                              {copiedToClipboard ? (
                                <Check className="h-3 w-3 mr-1" />
                              ) : (
                                <Copy className="h-3 w-3 mr-1" />
                              )}
                              {copiedToClipboard ? "Copied" : "Copy"}
                            </Button>
                            {onNavigateToEditor && (
                              <Button
                                size="sm"
                                className="h-7 text-xs"
                                onClick={() => onNavigateToEditor(message.contractContent!)}
                              >
                                <Wand2 className="h-3 w-3 mr-1" />
                                Edit
                              </Button>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {message.role === 'user' && (
                      <Avatar className="w-8 h-8 mt-1">
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                ))}
                
                {isGenerating && (
                  <div className="flex gap-3 justify-start">
                    <Avatar className="w-8 h-8 mt-1">
                      <AvatarFallback className="bg-purple-100 text-purple-600">
                        <Bot className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-muted rounded-lg p-3 text-sm">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Analyzing your request and generating contract...
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
            
            <Separator />
            
            {/* Input Area */}
            <div className="p-4">
              <div className="flex gap-2">
                <Textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Describe the contract you need... (Press Enter to send, Shift+Enter for new line)"
                  className="flex-1 min-h-[60px] resize-none"
                  disabled={isGenerating}
                />
                <div className="flex flex-col gap-2">
                  <Button
                    onClick={handleSendPrompt}
                    disabled={!prompt.trim() || isGenerating}
                    size="sm"
                    className="h-[30px]"
                  >
                    {isGenerating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                  {generatedContract && (
                    <Button
                      onClick={handleRegenerateContract}
                      disabled={isGenerating}
                      size="sm"
                      variant="outline"
                      className="h-[30px]"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contract Preview */}
        <Card className="flex flex-col">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Contract Preview</CardTitle>
              {generatedContract && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCopyContract}
                  >
                    {copiedToClipboard ? (
                      <Check className="h-4 w-4 mr-2" />
                    ) : (
                      <Copy className="h-4 w-4 mr-2" />
                    )}
                    {copiedToClipboard ? "Copied" : "Copy"}
                  </Button>
                  {onNavigateToEditor && (
                    <Button
                      size="sm"
                      onClick={() => onNavigateToEditor(generatedContract)}
                    >
                      <Wand2 className="h-4 w-4 mr-2" />
                      Edit in Document Engine
                    </Button>
                  )}
                </div>
              )}
            </div>
          </CardHeader>
          
          <CardContent className="flex-1 p-0">
            {generatedContract ? (
              <ScrollArea className="h-full px-4 pb-4">
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <pre className="whitespace-pre-wrap text-sm font-mono bg-muted p-4 rounded-lg">
                    {generatedContract}
                  </pre>
                </div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center p-6">
                <Sparkles className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Contract Generated Yet</h3>
                <p className="text-muted-foreground max-w-md mb-6">
                  Describe the contract you need in the chat, and I'll generate a professional draft for you to review and customize.
                </p>
                
                <div className="w-full max-w-md space-y-2">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Try these examples:</div>
                  {examplePrompts.slice(0, 3).map((example, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      className="w-full text-left justify-start text-xs h-auto py-2 px-3"
                      onClick={() => handleUsePrompt(example)}
                    >
                      <Lightbulb className="h-3 w-3 mr-2 flex-shrink-0" />
                      <span className="truncate">{example}</span>
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AIContractGenerator;
