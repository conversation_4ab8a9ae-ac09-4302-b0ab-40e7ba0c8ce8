import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import UnifiedImportModal from "../modals/UnifiedImportModal";
import AIContractGenerator from "./AIContractGenerator";
import { ArrowLeft, FileText, Wand2, Sparkles, Clock, Import, Copy, Users, Check, BookTemplate, Lightbulb, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";

const ContractCreationMethod = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("template");
  const [aiPrompt, setAiPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showTemplateFeatures, setShowTemplateFeatures] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);

  // Check if we're on the import route and show the import modal
  useEffect(() => {
    if (location.pathname === "/contracts/import") {
      setShowImportModal(true);
    }
  }, [location.pathname]);

  // Handle import modal close
  const handleImportModalChange = (open: boolean) => {
    setShowImportModal(open);
    // If we're on the import route and the modal is closed, navigate back to create
    if (!open && location.pathname === "/app/contracts/import") {
      navigate("/app/contracts/create");
    }
  };

  const handleUseTemplate = () => {
    navigate("/app/contracts/templates");
  };

  const handleStartFromScratch = () => {
    navigate("/app/contracts/wizard");
  };

  const handleAIGenerate = () => {
    if (!aiPrompt.trim()) return;

    setIsGenerating(true);
    // Simulate AI processing
    setTimeout(() => {
      setIsGenerating(false);
      navigate("/app/contracts/wizard?useAI=true&prompt=" + encodeURIComponent(aiPrompt));
    }, 2000);
  };

  const handleContractGenerated = (content: string) => {
    console.log("Contract generated:", content.substring(0, 100) + "...");
    // Store the generated content for later use
  };

  const handleNavigateToEditor = (content: string) => {
    // Navigate to the contract wizard with the generated content
    navigate("/app/contracts/wizard", {
      state: {
        generatedContent: content,
        useAI: true
      }
    });
  };

  return (
    <div className="w-full h-full bg-background p-4 overflow-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold">Create Contract</h1>
          <p className="text-base text-muted-foreground mt-1">
            Choose how you'd like to get started
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate("/contracts")}
          className="flex items-center h-9"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>




      {/* Main Content - Focused Three-Tab Interface */}
      <div className="max-w-4xl mx-auto">
        <Tabs
          defaultValue="template"
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-3 mb-8 h-12">
            <TabsTrigger value="template" className="flex items-center gap-2 text-base h-10">
              <FileText className="h-5 w-5" />
              <span>Templates</span>
            </TabsTrigger>
            <TabsTrigger value="scratch" className="flex items-center gap-2 text-base h-10">
              <Wand2 className="h-5 w-5" />
              <span>Custom</span>
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2 text-base h-10">
              <Sparkles className="h-5 w-5" />
              <span>AI Generate</span>
            </TabsTrigger>
          </TabsList>

              <TabsContent value="template" className="space-y-6">
                <Card>
                  <CardHeader className="pb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg font-medium">Use Template</CardTitle>
                    <CardDescription className="text-sm">
                      Start with professionally drafted templates
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <div className="grid grid-cols-2 gap-3 mb-6">
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-green-100 flex items-center justify-center">
                              <span className="text-xs">🔒</span>
                            </div>
                            <div className="font-medium text-sm">NDA</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Confidentiality protection
                          </div>
                        </Card>
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-blue-100 flex items-center justify-center">
                              <span className="text-xs">🛠️</span>
                            </div>
                            <div className="font-medium text-sm">Services</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Service agreements
                          </div>
                        </Card>
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-purple-100 flex items-center justify-center">
                              <span className="text-xs">👤</span>
                            </div>
                            <div className="font-medium text-sm">Employment</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Hiring contracts
                          </div>
                        </Card>
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-amber-100 flex items-center justify-center">
                              <span className="text-xs">⚖️</span>
                            </div>
                            <div className="font-medium text-sm">License</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Software licensing
                          </div>
                        </Card>
                      </div>

                      <div className="flex items-center gap-3 text-xs text-muted-foreground mb-4">
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Legally vetted</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <BookTemplate className="h-3 w-3 text-blue-600" />
                          <span>Fully customizable</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Lightbulb className="h-3 w-3 text-amber-600" />
                          <span>Smart suggestions</span>
                        </div>
                      </div>
                    </div>
                    <Button className="w-full h-10 text-sm" onClick={handleUseTemplate}>
                      Browse All Templates
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="scratch" className="space-y-6">
                <Card>
                  <CardHeader className="pb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <Wand2 className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg font-medium">Start from Scratch</CardTitle>
                    <CardDescription className="text-sm">
                      Build custom contracts with guided steps
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <div className="bg-secondary/20 p-4 rounded-lg mb-6">
                        <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                          <span className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary">7</span>
                          Step-by-step wizard
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">1</span>
                            </div>
                            <span>Jurisdiction & type</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">2</span>
                            </div>
                            <span>Parties information</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">3</span>
                            </div>
                            <span>Contract terms</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">4</span>
                            </div>
                            <span>Legal clauses</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">5</span>
                            </div>
                            <span>Industry provisions</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">6</span>
                            </div>
                            <span>Attachments</span>
                          </div>
                          <div className="flex items-center gap-2 md:col-span-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">7</span>
                            </div>
                            <span>Review & approval</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Full control</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Auto-save</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Unique agreements</span>
                        </div>
                      </div>
                    </div>
                    <Button className="w-full h-10 text-sm" onClick={handleStartFromScratch}>
                      Start Building Contract
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="ai" className="space-y-6">
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
                      <Sparkles className="h-4 w-4 text-purple-600" />
                    </div>
                    <h2 className="text-lg font-medium">AI-Assisted</h2>
                    <Badge className="bg-amber-100 text-amber-800 border-amber-300 text-xs py-0.5 px-2">Coming Soon</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Generate contracts from simple descriptions
                  </p>
                </div>

                <AIContractGenerator
                  onContractGenerated={handleContractGenerated}
                  onNavigateToEditor={handleNavigateToEditor}
                  className="w-full"
                />
              </TabsContent>
        </Tabs>

        {/* Secondary Options - Below main flow */}
        <div className="mt-12 pt-8 border-t border-border">
          <div className="text-center mb-6">
            <h4 className="text-lg font-medium mb-2">Other Ways to Create</h4>
            <p className="text-sm text-muted-foreground">Alternative methods for specific needs</p>
          </div>

          <div className="grid md:grid-cols-3 gap-4 max-w-3xl mx-auto">
            <Button
              variant="outline"
              className="h-auto py-4 flex flex-col items-center gap-2 hover:border-primary/50"
              onClick={() => handleImportModalChange(true)}
            >
              <Import className="h-6 w-6 text-muted-foreground" />
              <div className="text-center">
                <div className="font-medium text-sm">Import Document</div>
                <div className="text-xs text-muted-foreground">Upload existing files</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto py-4 flex flex-col items-center gap-2 hover:border-primary/50"
              onClick={() => navigate("/clone")}
            >
              <Copy className="h-6 w-6 text-muted-foreground" />
              <div className="text-center">
                <div className="font-medium text-sm">Clone Contract</div>
                <div className="text-xs text-muted-foreground">Copy existing contract</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto py-4 flex flex-col items-center gap-2 hover:border-primary/50"
              onClick={() => navigate("/request")}
            >
              <Users className="h-6 w-6 text-muted-foreground" />
              <div className="text-center">
                <div className="font-medium text-sm">Request Help</div>
                <div className="text-xs text-muted-foreground">Get legal team assistance</div>
              </div>
            </Button>
          </div>
        </div>
      </div>

      {/* Import Document Modal */}
      <UnifiedImportModal
        open={showImportModal}
        onOpenChange={handleImportModalChange}
        redirectToWizard={true}
      />
    </div>
  );
};



export default ContractCreationMethod;